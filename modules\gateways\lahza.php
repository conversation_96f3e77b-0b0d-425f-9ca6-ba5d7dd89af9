<?php
/**
 * Lahza.io Payment Gateway Module for WHMCS WIDDX Template
 *
 * This module integrates Lahza.io payment gateway with WHMCS
 * Optimized for WIDDX template with modern UI/UX
 * Supports ILS, USD, and JOD currencies with popup and redirect methods
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @link https://lahza.io/
 * @link https://docs.lahza.io/
 * @copyright Copyright (c) 2024 WIDDX
 * @license MIT License
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Define module related meta data.
 *
 * Enhanced metadata for better WHMCS integration
 *
 * @return array
 */
function lahza_MetaData()
{
    return array(
        'DisplayName' => 'Lahza.io Payment Gateway',
        'APIVersion' => '1.1',
        'DisableLocalCreditCardInput' => true,
        'TokenisedStorage' => false,
        'RequiresSSL' => true,
    );
}

/**
 * Define gateway configuration options.
 *
 * Enhanced configuration with modern options for WIDDX template
 *
 * @return array
 */
function lahza_config()
{
    return array(
        'FriendlyName' => array(
            'Type' => 'System',
            'Value' => 'Lahza.io Payment Gateway',
        ),
        'publicKey' => array(
            'FriendlyName' => 'Public Key',
            'Type' => 'text',
            'Size' => '60',
            'Default' => '',
            'Description' => 'Enter your Lahza.io Public Key (pk_test_... for test mode, pk_live_... for live mode)',
        ),
        'secretKey' => array(
            'FriendlyName' => 'Secret Key',
            'Type' => 'password',
            'Size' => '60',
            'Default' => '',
            'Description' => 'Enter your Lahza.io Secret Key (sk_test_... for test mode, sk_live_... for live mode)',
        ),
        'testMode' => array(
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Enable test mode for development and testing',
        ),
        'paymentMethod' => array(
            'FriendlyName' => 'Payment Method',
            'Type' => 'dropdown',
            'Options' => array(
                'popup' => 'Popup (Recommended)',
                'redirect' => 'Redirect',
            ),
            'Default' => 'popup',
            'Description' => 'Choose payment method: Popup for better UX, Redirect for compatibility',
        ),
        'allowedChannels' => array(
            'FriendlyName' => 'Allowed Payment Channels',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'card,bank,mobile_money',
            'Description' => 'Comma-separated list: card,bank,ussd,qr,mobile_money,bank_transfer',
        ),
        'webhookUrl' => array(
            'FriendlyName' => 'Webhook URL',
            'Type' => 'text',
            'Size' => '100',
            'Default' => '',
            'Description' => 'Your webhook URL (leave empty to auto-generate)',
        ),
        'customCSS' => array(
            'FriendlyName' => 'Custom CSS',
            'Type' => 'textarea',
            'Rows' => '4',
            'Description' => 'Add custom CSS for payment form styling (optional)',
        ),
        'enableLogging' => array(
            'FriendlyName' => 'Enable Logging',
            'Type' => 'yesno',
            'Description' => 'Enable detailed logging for debugging purposes',
        ),
    );
}

/**
 * Payment link generation for WIDDX template.
 *
 * Enhanced payment form with modern UI and better error handling
 *
 * @param array $params Payment Gateway Module Parameters
 * @return string HTML output for payment form
 */
function lahza_link($params)
{
    // Gateway Configuration Parameters
    $publicKey = $params['publicKey'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];
    $paymentMethod = $params['paymentMethod'] ?? 'popup';
    $allowedChannels = $params['allowedChannels'] ?? 'card,bank,mobile_money';
    $webhookUrl = $params['webhookUrl'] ?? '';
    $customCSS = $params['customCSS'] ?? '';
    $enableLogging = $params['enableLogging'] ?? false;

    // Invoice Parameters
    $invoiceId = $params['invoiceid'];
    $description = $params["description"];
    $amount = $params['amount'];
    $currencyCode = $params['currency'];

    // Client Parameters
    $firstname = $params['clientdetails']['firstname'];
    $lastname = $params['clientdetails']['lastname'];
    $email = $params['clientdetails']['email'];
    $phone = $params['clientdetails']['phonenumber'];
    $address1 = $params['clientdetails']['address1'];
    $city = $params['clientdetails']['city'];
    $country = $params['clientdetails']['country'];

    // System Parameters
    $companyName = $params['companyname'];
    $systemUrl = $params['systemurl'];
    $returnUrl = $params['returnurl'];
    $langPayNow = $params['langpaynow'];
    $moduleDisplayName = $params['name'];

    // Validate required parameters
    if (empty($publicKey) || empty($secretKey)) {
        return '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Lahza.io gateway not configured properly. Please contact administrator.</div>';
    }

    // Validate currency
    $supportedCurrencies = ['ILS', 'USD', 'JOD'];
    if (!in_array($currencyCode, $supportedCurrencies)) {
        return '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Currency ' . $currencyCode . ' is not supported by Lahza.io. Supported currencies: ' . implode(', ', $supportedCurrencies) . '</div>';
    }

    // Convert amount to lowest currency unit (multiply by 100)
    $amountInCents = (int)($amount * 100);

    // Generate unique transaction reference
    $transactionRef = 'WHMCS_' . $invoiceId . '_' . time() . '_' . substr(md5(uniqid()), 0, 8);

    // Parse allowed channels
    $channels = array_map('trim', explode(',', $allowedChannels));
    $channelsJson = json_encode($channels);

    // Auto-generate webhook URL if not provided
    if (empty($webhookUrl)) {
        $webhookUrl = $systemUrl . 'modules/gateways/callback/lahza.php';
    }

    // Prepare metadata
    $metadata = array(
        'invoice_id' => $invoiceId,
        'client_id' => $params['clientdetails']['userid'],
        'company_name' => $companyName,
        'whmcs_url' => $systemUrl,
        'payment_method' => $params['paymentmethod'],
        'client_name' => $firstname . ' ' . $lastname,
    );

    // Check if custom template exists
    $templatePath = ROOTDIR . '/templates/' . $params['template'] . '/payment/lahza/payment-form.tpl';

    if (file_exists($templatePath)) {
        // Use custom template
        $smarty = new Smarty();
        $smarty->setTemplateDir(ROOTDIR . '/templates/' . $params['template'] . '/');
        $smarty->setCompileDir(ROOTDIR . '/templates_c/');

        // Assign variables to template
        $smarty->assign('invoiceId', $invoiceId);
        $smarty->assign('description', $description);
        $smarty->assign('amount', $amount);
        $smarty->assign('currencyCode', $currencyCode);
        $smarty->assign('currency', $currencyCode);
        $smarty->assign('amountInCents', $amountInCents);
        $smarty->assign('firstname', $firstname);
        $smarty->assign('lastName', $lastname);
        $smarty->assign('firstName', $firstname);
        $smarty->assign('email', $email);
        $smarty->assign('phone', $phone);
        $smarty->assign('mobile', $phone);
        $smarty->assign('publicKey', $publicKey);
        $smarty->assign('transactionRef', $transactionRef);
        $smarty->assign('channelsJson', $channelsJson);
        $smarty->assign('metadataJson', json_encode($metadata));
        $smarty->assign('returnUrl', $returnUrl);
        $smarty->assign('langPayNow', $langPayNow);
        $smarty->assign('enableLogging', $enableLogging);
        $smarty->assign('customCSS', $customCSS);
        $smarty->assign('allowedChannels', $channels);
        $smarty->assign('clientName', $firstname . ' ' . $lastname);
        $smarty->assign('WEB_ROOT', $systemUrl);
        $smarty->assign('template', $params['template']);
        $smarty->assign('language', $params['language'] ?? 'english');

        try {
            $htmlOutput = $smarty->fetch('payment/lahza/payment-form.tpl');
        } catch (Exception $e) {
            // Fallback to inline HTML if template fails
            $htmlOutput = lahza_generateFallbackHTML($params, $invoiceId, $description, $amount, $currencyCode, $amountInCents, $firstname, $lastname, $email, $phone, $publicKey, $transactionRef, $channelsJson, $metadata, $returnUrl, $langPayNow, $enableLogging);
        }
    } else {
        // Fallback to inline HTML
        $htmlOutput = lahza_generateFallbackHTML($params, $invoiceId, $description, $amount, $currencyCode, $amountInCents, $firstname, $lastname, $email, $phone, $publicKey, $transactionRef, $channelsJson, $metadata, $returnUrl, $langPayNow, $enableLogging);
    }

    // Add enhanced JavaScript for better UX
    $htmlOutput .= '<script>
    document.addEventListener("DOMContentLoaded", function() {
        const payButton = document.getElementById("lahza-pay-btn");
        const errorElement = document.getElementById("lahza-payment-errors");
        const loadingElement = document.getElementById("lahza-payment-loading");
        const errorMessageSpan = errorElement.querySelector(".error-message");

        function showError(message) {
            errorMessageSpan.textContent = message;
            errorElement.style.display = "block";
            loadingElement.style.display = "none";
            payButton.disabled = false;
            payButton.innerHTML = \'<i class="fas fa-credit-card me-2"></i>' . $langPayNow . ' - ' . $amount . ' ' . $currencyCode . '\';
        }

        function showLoading() {
            errorElement.style.display = "none";
            loadingElement.style.display = "block";
            payButton.disabled = true;
            payButton.innerHTML = \'<i class="fas fa-spinner fa-spin me-2"></i>Processing...\';
        }

        payButton.addEventListener("click", function() {
            showLoading();

            try {
                const lahza = new LahzaPopup();
                lahza.newTransaction({
                    key: "' . $publicKey . '",
                    email: "' . $email . '",
                    mobile: "' . $phone . '",
                    firstName: "' . $firstname . '",
                    lastName: "' . $lastname . '",
                    amount: ' . $amountInCents . ',
                    currency: "' . $currencyCode . '",
                    ref: "' . $transactionRef . '",
                    channels: ' . $channelsJson . ',
                    metadata: ' . json_encode($metadata) . ',
                    onSuccess: function(transaction) {
                        // Log success if logging enabled
                        ' . ($enableLogging ? 'console.log("Lahza payment successful:", transaction);' : '') . '

                        // Redirect to return URL with transaction reference
                        window.location.href = "' . $returnUrl . '&reference=" + transaction.reference + "&status=success";
                    },
                    onCancel: function() {
                        showError("Payment was cancelled by user");
                        ' . ($enableLogging ? 'console.log("Lahza payment cancelled");' : '') . '
                    }
                });
            } catch (error) {
                console.error("Lahza payment initialization error:", error);
                showError("Error initializing payment: " + (error.message || "Unknown error"));
            }
        });
    });
    </script>';

    // Add custom CSS if provided
    if (!empty($customCSS)) {
        $htmlOutput .= '<style>' . $customCSS . '</style>';
    }

    return $htmlOutput;
}

/**
 * Generate fallback HTML when template is not available
 */
function lahza_generateFallbackHTML($params, $invoiceId, $description, $amount, $currencyCode, $amountInCents, $firstname, $lastname, $email, $phone, $publicKey, $transactionRef, $channelsJson, $metadata, $returnUrl, $langPayNow, $enableLogging) {
    $htmlOutput = '
    <div class="lahza-payment-container card border-0 shadow-sm">
        <div class="card-body p-4">
            <div class="payment-header text-center mb-4">
                <div class="payment-logo mb-3">
                    <i class="fas fa-credit-card text-primary" style="font-size: 2.5rem;"></i>
                </div>
                <h4 class="card-title mb-2">
                    <i class="fas fa-lock text-success"></i> Secure Payment
                </h4>
                <p class="text-muted mb-0">Powered by Lahza.io</p>
            </div>

            <div class="payment-details bg-light rounded p-3 mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">Invoice</small>
                        <div class="fw-bold">#' . $invoiceId . '</div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">Amount</small>
                        <div class="fw-bold text-primary">' . $amount . ' ' . $currencyCode . '</div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <small class="text-muted">Description</small>
                        <div class="fw-bold">' . htmlspecialchars($description) . '</div>
                    </div>
                </div>
            </div>

            <button type="button" id="lahza-pay-btn" class="btn btn-primary btn-lg w-100 mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; padding: 12px;">
                <i class="fas fa-credit-card me-2"></i>
                ' . $langPayNow . ' - ' . $amount . ' ' . $currencyCode . '
            </button>

            <div class="payment-security text-center">
                <small class="text-muted">
                    <i class="fas fa-shield-alt text-success"></i>
                    Your payment is secured with 256-bit SSL encryption
                </small>
            </div>

            <div id="lahza-payment-errors" class="alert alert-danger mt-3" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i>
                <span class="error-message"></span>
            </div>

            <div id="lahza-payment-loading" class="text-center mt-3" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-2">
                    <small class="text-muted">Processing payment...</small>
                </div>
            </div>
        </div>
    </div>';

    // Add Lahza JS library
    $htmlOutput .= '<script src="https://js.lahza.io/inline.min.js"></script>';

    // Add enhanced JavaScript for better UX
    $htmlOutput .= '<script>
    document.addEventListener("DOMContentLoaded", function() {
        const payButton = document.getElementById("lahza-pay-btn");
        const errorElement = document.getElementById("lahza-payment-errors");
        const loadingElement = document.getElementById("lahza-payment-loading");
        const errorMessageSpan = errorElement.querySelector(".error-message");

        function showError(message) {
            errorMessageSpan.textContent = message;
            errorElement.style.display = "block";
            loadingElement.style.display = "none";
            payButton.disabled = false;
            payButton.innerHTML = \'<i class="fas fa-credit-card me-2"></i>' . $langPayNow . ' - ' . $amount . ' ' . $currencyCode . '\';
        }

        function showLoading() {
            errorElement.style.display = "none";
            loadingElement.style.display = "block";
            payButton.disabled = true;
            payButton.innerHTML = \'<i class="fas fa-spinner fa-spin me-2"></i>Processing...\';
        }

        payButton.addEventListener("click", function() {
            showLoading();

            try {
                const lahza = new LahzaPopup();
                lahza.newTransaction({
                    key: "' . $publicKey . '",
                    email: "' . $email . '",
                    mobile: "' . $phone . '",
                    firstName: "' . $firstname . '",
                    lastName: "' . $lastname . '",
                    amount: ' . $amountInCents . ',
                    currency: "' . $currencyCode . '",
                    ref: "' . $transactionRef . '",
                    channels: ' . $channelsJson . ',
                    metadata: ' . json_encode($metadata) . ',
                    onSuccess: function(transaction) {
                        ' . ($enableLogging ? 'console.log("Lahza payment successful:", transaction);' : '') . '
                        window.location.href = "' . $returnUrl . '&reference=" + transaction.reference + "&status=success";
                    },
                    onCancel: function() {
                        showError("Payment was cancelled by user");
                        ' . ($enableLogging ? 'console.log("Lahza payment cancelled");' : '') . '
                    }
                });
            } catch (error) {
                console.error("Lahza payment initialization error:", error);
                showError("Error initializing payment: " + (error.message || "Unknown error"));
            }
        });
    });
    </script>';

    return $htmlOutput;
}
