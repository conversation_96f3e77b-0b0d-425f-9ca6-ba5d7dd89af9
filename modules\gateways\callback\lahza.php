<?php
/**
 * <PERSON><PERSON>za.io Payment Gateway Callback Handler for WHMCS WIDDX
 *
 * Enhanced callback handler with webhook support and better error handling
 * Supports both GET redirects and POST webhooks from Lahza.io
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @link https://docs.lahza.io/payments/webhooks
 */

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    logTransaction($gatewayModuleName, $_REQUEST, "Module Not Activated");
    header("HTTP/1.1 403 Forbidden");
    exit("Module Not Activated");
}

/**
 * Log activity for debugging - Always log important events
 */
function logActivity($message, $data = null, $forceLog = false) {
    global $gatewayParams, $gatewayModuleName;

    // Always log important events, or when logging is enabled
    if ($forceLog || $gatewayParams['enableLogging']) {
        $logMessage = '[Lahza.io] ' . $message;
        if ($data) {
            $logMessage .= ' - Data: ' . json_encode($data);
        }
        logTransaction($gatewayModuleName, $logMessage, 'Debug');
    }
}

/**
 * Verify payment with Lahza.io API
 */
function verifyPayment($reference, $secretKey) {
    $url = 'https://api.lahza.io/transaction/verify/' . $reference;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Authorization: Bearer ' . $secretKey,
        'Content-Type: application/json',
        'Cache-Control: no-cache',
    ));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        logActivity('cURL Error during verification', $error);
        return false;
    }

    if ($httpCode !== 200) {
        logActivity('HTTP Error during verification', ['code' => $httpCode, 'response' => $response]);
        return false;
    }

    $result = json_decode($response, true);

    if (!$result || !isset($result['status']) || $result['status'] !== true) {
        logActivity('Invalid verification response', $result);
        return false;
    }

    return $result['data'];
}

/**
 * Verify webhook signature
 */
function verifyWebhookSignature($payload, $signature, $secretKey) {
    $expectedSignature = hash_hmac('sha256', $payload, $secretKey);
    return hash_equals($expectedSignature, $signature);
}

// Handle different types of requests
$requestMethod = $_SERVER['REQUEST_METHOD'];
$success = false;
$transactionId = '';
$paymentAmount = '';
$paymentFee = '';
$invoiceId = '';
$transactionStatus = '';

// Always log callback attempts
logActivity('Callback received', [
    'method' => $requestMethod,
    'get' => $_GET,
    'post_exists' => !empty(file_get_contents('php://input')),
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
    'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
], true);

if ($requestMethod === 'POST') {
    // Handle webhook notification
    $payload = file_get_contents('php://input');
    $signature = $_SERVER['HTTP_X_LAHZA_SIGNATURE'] ?? '';

    logActivity('Webhook received', ['payload' => $payload, 'signature' => $signature]);

    // Verify webhook signature
    if (!verifyWebhookSignature($payload, $signature, $gatewayParams['secretKey'])) {
        logActivity('Invalid webhook signature');
        http_response_code(401);
        die('Invalid signature');
    }

    $webhookData = json_decode($payload, true);

    if (!$webhookData || !isset($webhookData['event'])) {
        logActivity('Invalid webhook data');
        http_response_code(400);
        die('Invalid webhook data');
    }

    $event = $webhookData['event'];
    $data = $webhookData['data'] ?? [];

    logActivity('Processing webhook event', ['event' => $event, 'data' => $data]);

    // Handle different webhook events
    switch ($event) {
        case 'charge.success':
            if (isset($data['reference']) && isset($data['metadata']['invoice_id'])) {
                $transactionId = $data['reference'];
                $invoiceId = $data['metadata']['invoice_id'];
                $paymentAmount = $data['amount'] / 100; // Convert from cents
                $paymentFee = ($data['fees'] ?? 0) / 100;
                $transactionStatus = $data['status'];

                if ($transactionStatus === 'success') {
                    $success = true;
                    logActivity('Payment successful via webhook', [
                        'invoice_id' => $invoiceId,
                        'transaction_id' => $transactionId,
                        'amount' => $paymentAmount
                    ]);
                }
            }
            break;

        case 'refund.processed':
            // Handle refund if needed
            logActivity('Refund processed', $data);
            break;

        default:
            logActivity('Unhandled webhook event', ['event' => $event]);
            break;
    }

    // Respond with 200 OK to acknowledge webhook
    http_response_code(200);
    echo 'OK';

} else {
    // Handle GET request (redirect callback)
    $reference = $_GET['reference'] ?? '';
    $status = $_GET['status'] ?? '';

    logActivity('Redirect callback received', ['reference' => $reference, 'status' => $status], true);

    if (empty($reference)) {
        logActivity('Missing transaction reference in callback', $_GET, true);
        die('Missing transaction reference');
    }

    // Verify payment with Lahza.io API
    $paymentData = verifyPayment($reference, $gatewayParams['secretKey']);

    if (!$paymentData) {
        logActivity('Payment verification failed', ['reference' => $reference]);
        die('Payment verification failed');
    }

    $transactionId = $paymentData['reference'];
    $paymentAmount = $paymentData['amount'] / 100; // Convert from cents
    $paymentFee = ($paymentData['fees'] ?? 0) / 100;
    $transactionStatus = $paymentData['status'];

    // Extract invoice ID from metadata or reference
    if (isset($paymentData['metadata']['invoice_id'])) {
        $invoiceId = $paymentData['metadata']['invoice_id'];
        logActivity('Invoice ID found in metadata', ['invoice_id' => $invoiceId], true);
    } else {
        // Try to extract from reference if it follows our pattern
        if (preg_match('/^WHMCS_(\d+)_/', $transactionId, $matches)) {
            $invoiceId = $matches[1];
            logActivity('Invoice ID extracted from reference', ['invoice_id' => $invoiceId, 'reference' => $transactionId], true);
        } else {
            // Try alternative patterns
            if (preg_match('/invoice[_-]?(\d+)/i', $transactionId, $matches)) {
                $invoiceId = $matches[1];
                logActivity('Invoice ID found using alternative pattern', ['invoice_id' => $invoiceId, 'reference' => $transactionId], true);
            } else {
                logActivity('Could not extract invoice ID', ['reference' => $transactionId, 'metadata' => $paymentData['metadata'] ?? []], true);
            }
        }
    }

    if ($transactionStatus === 'success') {
        $success = true;
        logActivity('Payment verified successfully', [
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $paymentAmount
        ]);
    } else {
        logActivity('Payment not successful', [
            'status' => $transactionStatus,
            'reference' => $reference
        ]);
    }
}

// Process the payment if successful
if ($success && !empty($invoiceId) && !empty($transactionId)) {

    logActivity('Processing payment', [
        'invoice_id' => $invoiceId,
        'transaction_id' => $transactionId,
        'amount' => $paymentAmount,
        'fee' => $paymentFee
    ], true);

    try {
        /**
         * Validate Callback Invoice ID.
         *
         * Checks invoice ID is a valid invoice number. Note it will count an
         * invoice in any status as valid.
         *
         * Performs a die upon encountering an invalid Invoice ID.
         *
         * Returns a normalised invoice ID.
         */
        $invoiceId = checkCbInvoiceID($invoiceId, $gatewayParams['name']);
        logActivity('Invoice ID validated', ['invoice_id' => $invoiceId], true);

        /**
         * Check Callback Transaction ID.
         *
         * Performs a check for any existing transactions with the same given
         * transaction number.
         *
         * Performs a die upon encountering a duplicate.
         */
        checkCbTransID($transactionId);
        logActivity('Transaction ID validated', ['transaction_id' => $transactionId], true);
    } catch (Exception $e) {
        logActivity('Validation error', [
            'error' => $e->getMessage(),
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId
        ], true);

        if ($requestMethod === 'GET') {
            die('Validation error: ' . $e->getMessage());
        } else {
            http_response_code(400);
            die('Validation error');
        }
    }

    /**
     * Log Transaction.
     *
     * Add an entry to the Gateway Log for debugging purposes.
     */
    logTransaction($gatewayParams['name'], [
        'Transaction ID' => $transactionId,
        'Invoice ID' => $invoiceId,
        'Amount' => $paymentAmount,
        'Fee' => $paymentFee,
        'Status' => $transactionStatus,
        'Method' => $requestMethod,
    ], 'Successful');

    /**
     * Add Invoice Payment.
     *
     * Applies a payment transaction entry to the given invoice ID.
     *
     * @param int $invoiceId         Invoice ID
     * @param string $transactionId  Transaction ID
     * @param float $paymentAmount   Amount paid (defaults to full balance)
     * @param float $paymentFee      Payment fee (optional)
     * @param string $gatewayModule  Gateway module name
     */
    try {
        addInvoicePayment(
            $invoiceId,
            $transactionId,
            $paymentAmount,
            $paymentFee,
            $gatewayModuleName
        );

        logActivity('Payment processed successfully', [
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $paymentAmount,
            'fee' => $paymentFee
        ], true);

    } catch (Exception $e) {
        logActivity('Payment processing error', [
            'error' => $e->getMessage(),
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $paymentAmount
        ], true);

        if ($requestMethod === 'GET') {
            die('Payment processing error: ' . $e->getMessage());
        } else {
            http_response_code(500);
            die('Payment processing error');
        }
    }

    // Redirect to invoice if this was a GET request
    if ($requestMethod === 'GET') {
        header('Location: ' . $gatewayParams['systemurl'] . 'viewinvoice.php?id=' . $invoiceId . '&paymentsuccess=true');
        exit;
    }

} elseif ($requestMethod === 'GET') {
    // Handle failed payment redirect or missing data
    if (!empty($invoiceId)) {
        logActivity('Payment failed or cancelled', [
            'invoice_id' => $invoiceId,
            'reference' => $_GET['reference'] ?? '',
            'success' => $success,
            'transaction_id' => $transactionId
        ], true);
        header('Location: ' . $gatewayParams['systemurl'] . 'viewinvoice.php?id=' . $invoiceId . '&paymentfailed=true');
    } else {
        logActivity('No invoice ID found in callback', [
            'reference' => $_GET['reference'] ?? '',
            'success' => $success,
            'transaction_id' => $transactionId,
            'get_params' => $_GET
        ], true);
        header('Location: ' . $gatewayParams['systemurl'] . 'clientarea.php');
    }
    exit;
} else {
    // Handle POST webhook without redirect
    logActivity('Webhook processed', [
        'success' => $success,
        'invoice_id' => $invoiceId,
        'transaction_id' => $transactionId
    ], true);
}
